<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<body>
    <!-- 分页片段 -->
    <div th:fragment="pagination(page, url)">
        <nav aria-label="Page navigation" class="d-flex justify-content-center">
            <ul class="pagination mb-0">
                <!-- 首页 -->
                <li class="page-item" th:classappend="${page.first ? 'disabled' : ''}">
                    <a class="page-link pagination-link" th:href="${page.first ? '#' : url + '?page=0'}" th:data-page="0" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
                
                <!-- 上一页 -->
                <li class="page-item" th:classappend="${page.first ? 'disabled' : ''}">
                    <a class="page-link pagination-link" th:href="${page.first ? '#' : url + '?page=' + (page.number - 1)}" th:data-page="${page.number - 1}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                
                <!-- 页码 -->
                <th:block th:with="
                    totalPages=${page.totalPages},
                    currentPage=${page.number},
                    maxVisible=5,
                    halfVisible=${maxVisible / 2},
                    startPage=${currentPage - halfVisible < 0 ? 0 : (currentPage + halfVisible >= totalPages ? (totalPages - maxVisible > 0 ? totalPages - maxVisible : 0) : currentPage - halfVisible)},
                    endPage=${startPage + maxVisible - 1 < totalPages - 1 ? startPage + maxVisible - 1 : totalPages - 1}">
                    
                    <!-- 第一页（如果不在可见范围内） -->
                    <li class="page-item" th:if="${startPage > 0}" th:classappend="${currentPage == 0 ? 'active' : ''}">
                        <a class="page-link pagination-link" th:href="${url + '?page=0'}" th:data-page="0" th:text="1">1</a>
                    </li>
                    
                    <!-- 左侧省略号 -->
                    <li class="page-item disabled" th:if="${startPage > 1}">
                        <span class="page-link">...</span>
                    </li>
                    
                    <!-- 中间页码 -->
                    <li class="page-item" th:each="i : ${#numbers.sequence(startPage, endPage)}" 
                        th:classappend="${i == currentPage ? 'active' : ''}">
                        <a class="page-link pagination-link" th:href="${url + '?page=' + i}" th:data-page="${i}" th:text="${i + 1}">1</a>
                    </li>
                    
                    <!-- 右侧省略号 -->
                    <li class="page-item disabled" th:if="${endPage < totalPages - 2}">
                        <span class="page-link">...</span>
                    </li>
                    
                    <!-- 最后一页（如果不在可见范围内） -->
                    <li class="page-item" th:if="${endPage < totalPages - 1}" th:classappend="${currentPage == totalPages - 1 ? 'active' : ''}">
                        <a class="page-link pagination-link" th:href="${url + '?page=' + (totalPages - 1)}" th:data-page="${totalPages - 1}" th:text="${totalPages}">最后页</a>
                    </li>
                </th:block>
                
                <!-- 下一页 -->
                <li class="page-item" th:classappend="${page.last ? 'disabled' : ''}">
                    <a class="page-link pagination-link" th:href="${page.last ? '#' : url + '?page=' + (page.number + 1)}" th:data-page="${page.number + 1}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                
                <!-- 末页 -->
                <li class="page-item" th:classappend="${page.last ? 'disabled' : ''}">
                    <a class="page-link pagination-link" th:href="${page.last ? '#' : url + '?page=' + (page.totalPages - 1)}" th:data-page="${page.totalPages - 1}" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
            </ul>
            <!-- 显示页码信息 -->
            <div class="pagination-info ms-3 align-self-center text-muted" 
                 th:if="${page.totalPages > 0}" 
                 th:text="'第 ' + (${page.number} + 1) + ' 页 / 共 ' + ${page.totalPages} + ' 页 (' + ${page.totalElements} + ' 条记录)'"></div>
        </nav>
        
        <!-- 保留分页链接点击处理脚本，保持现有JavaScript功能兼容性 -->
        <script th:inline="javascript">
            // 使用IIFE包裹以避免变量污染
            (function() {
                // 获取当前URL参数
                function getCurrentUrlParams() {
                    const params = new URLSearchParams(window.location.search);
                    // 不删除page参数，让分页处理逻辑自己决定如何处理
                    return params;
                }
                
                // 初始化时处理所有分页链接
                document.addEventListener('DOMContentLoaded', function() {
                    console.log('分页片段初始化');
                    // 获取当前URL参数
                    const params = getCurrentUrlParams();
                    const currentPathname = window.location.pathname;
                    
                    // 处理所有分页链接，确保它们包含当前所有参数
                    document.querySelectorAll('.pagination-link').forEach(function(link) {
                        if (link.getAttribute('href') !== '#') {
                            try {
                                // 检查当前是否在高级搜索页面
                                const isAdvancedSearch = currentPathname.includes('/advanced-search');
                                
                                // 如果是高级搜索页面，使用完整的当前URL作为基础
                                let baseUrl;
                                if (isAdvancedSearch) {
                                    baseUrl = currentPathname; // 保持在高级搜索页面
                                } else {
                                    // 否则使用链接原始的基本URL
                                    baseUrl = link.getAttribute('href').split('?')[0];
                                }
                                
                                const pageParams = new URLSearchParams();
                                const targetPage = link.getAttribute('data-page');

                                // 从链接的href中解析出应该使用的页码参数名
                                const linkHref = link.getAttribute('href');
                                let pageParamName = 'page'; // 默认使用page

                                if (linkHref && linkHref.includes('submitPage=')) {
                                    pageParamName = 'submitPage';
                                }

                                // 设置正确的页码参数
                                pageParams.set(pageParamName, targetPage);

                                // 合并所有参数，但不覆盖刚设置的页码参数
                                for (const [key, value] of params.entries()) {
                                    if (key !== pageParamName) {
                                        pageParams.set(key, value);
                                    }
                                }
                                
                                // 更新链接URL
                                link.href = baseUrl + '?' + pageParams.toString();
                                console.log('更新链接:', link.href);
                            } catch (e) {
                                console.error('更新分页链接时出错:', e);
                            }
                        }
                    });
                });
            })();
        </script>
    </div>
</body>
</html> 