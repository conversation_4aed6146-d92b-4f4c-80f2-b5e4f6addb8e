package com.mylog.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mylog.model.SubTask;
import com.mylog.repository.SubTaskRepository;
import com.mylog.service.SubTaskService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class SubTaskServiceImpl implements SubTaskService {

    @Autowired
    private SubTaskRepository subTaskRepository;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Override
    public List<SubTask> findAllSubTasks() {
        return subTaskRepository.findAll();
    }

    @Override
    public Optional<SubTask> findSubTaskById(Long id) {
        return subTaskRepository.findById(id);
    }

    @Override
    public List<SubTask> findSubTasksByTaskId(Long taskId) {
        return subTaskRepository.querySubTasksByTaskId(taskId);
    }

    @Override
    public Page<SubTask> findSubTasksByTaskId(Long taskId, Pageable pageable) {
        return subTaskRepository.querySubTasksByTaskIdPaged(taskId, pageable);
    }

    @Override
    public SubTask saveSubTask(SubTask subTask) {
        if (subTask.getSubTaskId() == null) {
            // 如果是新建子任务，设置序号
            subTask.setSequenceNumber(getNextSequenceNumber(subTask.getTaskId()));
        }
        
        // 保存子任务
        SubTask savedSubTask = subTaskRepository.save(subTask);
        
        // 发布事件通知父任务更新最后评论日期
        try {
            eventPublisher.publishEvent(new TaskUpdateEvent(subTask.getTaskId()));
        } catch (Exception e) {
            // 记录错误但不影响子任务的保存
            System.err.println("发布任务更新事件时出错: " + e.getMessage());
        }
        
        return savedSubTask;
    }

    @Override
    public void deleteSubTask(Long id) {
        subTaskRepository.deleteById(id);
    }

    @Override
    public Integer getNextSequenceNumber(Long taskId) {
        Integer maxSequence = subTaskRepository.findMaxSequenceNumberByTaskId(taskId);
        return (maxSequence == null) ? 1 : maxSequence + 1;
    }

    @Override
    public List<SubTask> searchSubTasks(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return findAllSubTasks();
        }
        return subTaskRepository.findByLogContentContaining(keyword);
    }

    @Override
    public List<SubTask> findSubTasksByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return subTaskRepository.findByCreatedDateBetween(
            startDate.format(formatter),
            endDate.format(formatter)
        );
    }

    @Override
    public Optional<SubTask> findLatestSubTaskByTaskId(Long taskId) {
        return subTaskRepository.queryLatestSubTaskByTaskId(taskId);
    }

    @Override
    public List<SubTask> findLatestSubTasksByTaskIds(List<Long> taskIds) {
        if (taskIds == null || taskIds.isEmpty()) {
            return new ArrayList<>();
        }
        return subTaskRepository.findLatestSubTasksByTaskIds(taskIds);
    }
} 