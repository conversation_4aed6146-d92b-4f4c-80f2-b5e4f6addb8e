<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head(${task.taskId != null ? '编辑任务 - ' + task.taskName : '新建任务'})}">
    <meta charset="UTF-8">
    <title>新建/编辑任务</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script#customScript})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2" th:text="${task.taskId == null ? '新建任务' : '编辑任务'}">任务表单</h1>
        </div>

        <div class="row">
            <div class="col-md-12">
                <form th:action="@{/tasks/save}" method="post" th:object="${task}" class="needs-validation" novalidate>
                    <input type="hidden" th:field="*{taskId}" />
                    <input type="hidden" th:field="*{projectId}" />
                    <input type="hidden" name="referer" th:value="${referer}" />

                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">基本信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="taskName" class="form-label">任务名称<span class="text-danger">*</span></label>
                                    <select class="form-select select2-control" id="taskName" th:field="*{taskName}" required>
                                        <option value="">请选择或输入任务名称</option>
                                        <option th:each="name : ${taskNames}"
                                                th:value="${name}"
                                                th:text="${name}"
                                                th:selected="${name == task.taskName}">任务名称</option>
                                        <option th:if="${task.taskName != null && !taskNames.contains(task.taskName)}"
                                                th:value="${task.taskName}"
                                                th:text="${task.taskName}"
                                                selected>[[${task.taskName}]]</option>
                                    </select>
                                    <div class="invalid-feedback">请输入任务名称</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="responsible" class="form-label">负责人<span class="text-danger">*</span></label>
                                    <select class="form-select" id="responsible" th:field="*{responsible}" required>
                                        <option value="">请选择负责人</option>
                                        <option th:each="person : ${personnel}"
                                                th:value="${person}"
                                                th:text="${person}">张三</option>
                                    </select>
                                    <div class="invalid-feedback">请选择负责人</div>
                                </div>
                            </div>                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="status" class="form-label">状态<span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" th:field="*{status}" required>
                                        <!-- 只有新建任务时才显示"未开始"选项 -->
                                        <option th:if="${task.taskId == null}" value="未开始">未开始</option>
                                        <option value="进行中">进行中</option>
                                        <!-- 编辑现有任务时，如果当前状态是"已暂停"，显示该选项 -->
                                        <option th:if="${task.taskId != null && task.status == '已暂停'}" value="已暂停">已暂停</option>
                                        <!-- 管理员可以选择"已完成"状态 -->
                                        <option th:if="${#authorization.expression('hasRole(''ADMIN'')')}" value="已完成">已完成</option>
                                        <!-- 编辑现有任务时，如果当前状态是"已完成"且非管理员，也要显示该选项（保持当前状态） -->
                                        <option th:if="${task.taskId != null && task.status == '已完成' && !#authorization.expression('hasRole(''ADMIN'')')}" value="已完成">已完成</option>
                                    </select>
                                    <div class="invalid-feedback">请选择状态</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="risk" class="form-label">风险等级</label>
                                    <select class="form-select" id="risk" th:field="*{risk}">
                                        <option value="正常">正常</option>
                                        <option value="低">低</option>
                                        <option value="中">中</option>
                                        <option value="高">高</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="type" class="form-label">任务类型<span class="text-danger">*</span></label>
                                    <select class="form-select" id="type" th:field="*{type}" required>
                                        <option value="">请选择任务类型</option>
                                        <option th:each="type : ${taskTypes}"
                                                th:value="${type}"
                                                th:text="${type}">任务类型</option>
                                    </select>
                                    <div class="invalid-feedback">请选择任务类型</div>
                                </div>                                <div class="col-md-6">
                                    <label for="ratio" class="form-label">比例</label>
                                    <input type="number" step="0.01" class="form-control" id="ratio" th:field="*{ratio}">
                                    <small class="text-muted">可手动输入或根据额定工期自动计算（任务额定工期÷项目额定工期）</small>
                                </div>
                            </div>                            <div class="row mb-3">                                <div class="col-md-6">
                                    <label for="ratedDurationDays" class="form-label">额定工期（天）</label>
                                    <input type="number" step="0.01" class="form-control" id="ratedDurationDays" th:field="*{ratedDurationDays}"><small class="text-muted">
                                        可手动输入或根据比例自动计算（项目额定工期×比例），修改后将自动计算比例
                                        <span th:if="${projectRatedDuration != null and projectRatedDuration > 0}">
                                            - 当前项目额定工期: [[${projectRatedDuration}]] 天
                                        </span>
                                        <span th:if="${projectRatedDuration == null or projectRatedDuration <= 0}" class="text-warning">
                                            - 项目额定工期未设置
                                        </span>
                                    </small>
                                </div>

                                                                <div class="col-md-6">
                                    <label for="progress" class="form-label">进度</label>
                                    <div class="progress mb-2" style="height: 24px; cursor: pointer;" id="progressBar">
                                        <div class="progress-bar" role="progressbar"
                                             th:style="'width: ' + (${task.progress != null ? task.progress : 0}) + '%'"
                                             th:text="(${task.progress != null ? task.progress : 0}) + '%'"
                                             th:aria-valuenow="${task.progress != null ? task.progress : 0}"
                                             aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="input-group">
                                        <input type="number" step="1" class="form-control" id="progress" th:field="*{progress}" min="0" max="100" th:value="${task.progress != null ? task.progress : 0}">
                                        <span class="input-group-text">%</span>
                                    </div>
                                    <small class="text-muted">请输入0到100之间的整数或直接拖动进度条</small>
                                </div>

                            </div>


                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="remarks" class="form-label">备注</label>
                                    <textarea class="form-control" id="remarks" th:field="*{remarks}" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 隐藏字段，保留实际开始和结束日期的值 -->
                    <input type="hidden" id="actualStartDate" name="actualStartDate" th:value="${task.actualStartDate}" />
                    <input type="hidden" id="actualEndDate" name="actualEndDate" th:value="${task.actualEndDate}" />
                    <input type="hidden" name="createdDate" th:value="${task.createdDate}" />

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a th:href="${referer != null ? referer : '#'}" onclick="return handleCancel(event)" class="btn btn-secondary me-2">取消</a>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>    <script id="customScript" th:inline="javascript">
        // 项目额定工期数据（从后端安全传递，只传递必要的数值）
        var projectRatedDurationValue = /*[[${projectRatedDuration}]]*/ 0;
        window.projectRatedDuration = projectRatedDurationValue;
        console.log('初始化项目额定工期:', window.projectRatedDuration);
        console.log('项目额定工期类型:', typeof window.projectRatedDuration);
          // 添加自定义CSS样式来防止比例和额定工期字段显示验证错误
        const customStyles = document.createElement('style');
        customStyles.textContent = `
            /* 完全禁用特定字段的验证样式 */
            #ratio, #ratedDurationDays {
                border-color: #ced4da !important;
                background-image: none !important;
            }
            
            #ratio:invalid, #ratedDurationDays:invalid,
            #ratio.is-invalid, #ratedDurationDays.is-invalid,
            .needs-validation.was-validated #ratio:invalid,
            .needs-validation.was-validated #ratedDurationDays:invalid {
                border-color: #ced4da !important;
                background-image: none !important;
                box-shadow: none !important;
            }
            
            /* 隐藏特定字段的验证反馈 */
            #ratio ~ .invalid-feedback,
            #ratedDurationDays ~ .invalid-feedback,
            .needs-validation.was-validated #ratio ~ .invalid-feedback,
            .needs-validation.was-validated #ratedDurationDays ~ .invalid-feedback {
                display: none !important;
            }
        `;
        document.head.appendChild(customStyles);
        
        // 防止在浏览器历史中记录该页面
        history.replaceState(null, document.title, window.location.href);

        // 返回到引用页面的函数
        function handleReturn() {
            var referer = document.querySelector('input[name="referer"]')?.value;
            var projectId = new URLSearchParams(window.location.search).get('projectId');

            if (projectId) {
                window.location.replace('/projects/' + projectId);
            } else if (referer) {
                window.location.replace(referer);
            } else {
                window.location.replace('/tasks/order-tasks');
            }
            return false;
        }

        // 引入必要的CSS
        const linkSelect2CSS = document.createElement('link');
        linkSelect2CSS.rel = 'stylesheet';
        linkSelect2CSS.href = 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css';
        document.head.appendChild(linkSelect2CSS);

        const linkSelect2Theme = document.createElement('link');
        linkSelect2Theme.rel = 'stylesheet';
        linkSelect2Theme.href = 'https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css';
        document.head.appendChild(linkSelect2Theme);

        // 引入必要的JS
        const scriptSelect2 = document.createElement('script');
        scriptSelect2.src = 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js';
        document.body.appendChild(scriptSelect2);

        // 处理返回按钮点击
        document.addEventListener('DOMContentLoaded', function() {
            // 标记为表单页面
            if (window.markAsFormPage) {
                markAsFormPage();
            }

            // 拦截所有取消按钮的点击事件
            document.querySelectorAll('a.btn-secondary').forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    handleReturn();
                });
            });

            // 进度条相关功能
            const progressBar = document.getElementById('progressBar');
            const progressInput = document.getElementById('progress');
            const progressBarInner = progressBar.querySelector('.progress-bar');

            // 鼠标点击进度条时更新进度
            progressBar.addEventListener('click', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const percent = Math.round((x / rect.width) * 100);
                updateProgress(Math.min(Math.max(percent, 0), 100));
            });

            // 鼠标拖动进度条时更新进度
            let isDragging = false;

            progressBar.addEventListener('mousedown', function() {
                isDragging = true;
            });

            document.addEventListener('mousemove', function(e) {
                if (isDragging) {
                    const rect = progressBar.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const percent = Math.round((x / rect.width) * 100);
                    updateProgress(Math.min(Math.max(percent, 0), 100));
                }
            });

            document.addEventListener('mouseup', function() {
                isDragging = false;
            });

            // 输入框值变化时更新进度条
            progressInput.addEventListener('input', function() {
                updateProgress(this.value);
            });

            // 更新进度条和输入框的值
            function updateProgress(value) {
                value = Math.min(Math.max(parseInt(value) || 0, 0), 100);
                progressInput.value = value;
                progressBarInner.style.width = value + '%';
                progressBarInner.textContent = value + '%';
            }
        });

        // 强制禁用浏览器的后退功能
        window.addEventListener('popstate', function() {
            handleReturn();
        });        // 等待Select2加载完成后初始化
        scriptSelect2.onload = function() {
            // 初始化Select2
            $('.select2-control').select2({
                theme: 'bootstrap-5',
                width: '100%',
                tags: true,
                placeholder: '请选择或输入任务名称',
                allowClear: true,
                language: {
                    noResults: function() {
                        return '没有找到匹配的选项';
                    }
                }
            });            // 为比例和额定工期字段设置完全保护逻辑
            const ratioField = document.getElementById('ratio');
            const durationField = document.getElementById('ratedDurationDays');
            
            // 强制移除所有可能的验证属性
            function forceRemoveValidation() {
                if (ratioField) {
                    ratioField.setCustomValidity('');
                    ratioField.removeAttribute('min');
                    ratioField.removeAttribute('max');
                    ratioField.removeAttribute('required');
                    ratioField.classList.remove('is-invalid', 'is-valid');
                    // 覆盖checkValidity方法，始终返回true
                    ratioField.checkValidity = function() { return true; };
                    ratioField.reportValidity = function() { return true; };
                }
                if (durationField) {
                    durationField.setCustomValidity('');
                    durationField.removeAttribute('min');
                    durationField.removeAttribute('max');
                    durationField.removeAttribute('required');
                    durationField.classList.remove('is-invalid', 'is-valid');
                    // 覆盖checkValidity方法，始终返回true
                    durationField.checkValidity = function() { return true; };
                    durationField.reportValidity = function() { return true; };
                }
            }
            
            // 立即执行一次
            forceRemoveValidation();
            
            // 频繁执行以确保持续生效
            setInterval(forceRemoveValidation, 100);// 监听状态变化
            $('#status').change(function() {
                var status = $(this).val();
                if(status === '已完成') {
                    $('#progress').val(100);
                    // 移除自动设置比例为1.0的逻辑，因为比例应该保持用户设置的值
                    // $('#ratio').val(1.0);
                }
            });

            // 设置实际开始和结束日期的最大值为当前日期
            var today = new Date().toISOString().split('T')[0];
            $('#actualStartDate').attr('max', today);
            $('#actualEndDate').attr('max', today);

            // 监听实际开始日期变化
            $('#actualStartDate').change(function() {
                var startDate = $(this).val();
                $('#actualEndDate').attr('min', startDate);
            });            // 监听实际结束日期变化
            $('#actualEndDate').change(function() {
                var endDate = $(this).val();
                $('#actualStartDate').attr('max', endDate);
            });            // 监听比例变化，自动计算额定工期
            $('#ratio').on('input change keyup paste', function() {
                // 强制清除任何验证状态
                this.setCustomValidity('');
                this.removeAttribute('min');
                this.removeAttribute('max');
                this.classList.remove('is-invalid', 'is-valid');
                calculateRatedDurationFromRatio();
            });

            // 监听额定工期变化，自动计算比例
            $('#ratedDurationDays').on('input change keyup paste', function() {
                // 强制清除任何验证状态
                this.setCustomValidity('');
                this.removeAttribute('min');
                this.removeAttribute('max');
                this.classList.remove('is-invalid', 'is-valid');
                calculateRatioFromDuration();
            });

            // 页面加载完成后，如果比例有值，自动计算一次额定工期
            if ($('#ratio').val() && parseFloat($('#ratio').val()) > 0) {
                calculateRatedDurationFromRatio();
            }
        };        // 在form提交时验证
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms)
                .forEach(function (form) {                    form.addEventListener('submit', function (event) {
                        // 获取比例和额定工期字段
                        const ratioField = form.querySelector('#ratio');
                        const durationField = form.querySelector('#ratedDurationDays');
                        
                        // 强制清除这两个字段的任何验证约束和状态
                        if (ratioField) {
                            ratioField.setCustomValidity('');
                            ratioField.removeAttribute('min');
                            ratioField.removeAttribute('max');
                            ratioField.classList.remove('is-invalid', 'is-valid');
                            console.log('提交时比例值:', ratioField.value);
                        }
                        if (durationField) {
                            durationField.setCustomValidity('');
                            durationField.removeAttribute('min');
                            durationField.removeAttribute('max');
                            durationField.classList.remove('is-invalid', 'is-valid');
                            console.log('提交时额定工期值:', durationField.value);
                        }
                        
                        // 临时创建一个表单副本用于验证，排除ratio和duration字段
                        const tempForm = form.cloneNode(true);
                        const tempRatioField = tempForm.querySelector('#ratio');
                        const tempDurationField = tempForm.querySelector('#ratedDurationDays');
                        
                        if (tempRatioField) {
                            tempRatioField.removeAttribute('required');
                            tempRatioField.removeAttribute('min');
                            tempRatioField.removeAttribute('max');
                        }
                        if (tempDurationField) {
                            tempDurationField.removeAttribute('required');
                            tempDurationField.removeAttribute('min');
                            tempDurationField.removeAttribute('max');
                        }
                        
                        // 检查其他字段的验证状态
                        let otherFieldsValid = true;
                        const requiredFields = form.querySelectorAll('[required]');
                        requiredFields.forEach(field => {
                            if (field.id !== 'ratio' && field.id !== 'ratedDurationDays') {
                                if (!field.value.trim()) {
                                    otherFieldsValid = false;
                                    field.classList.add('is-invalid');
                                }
                            }
                        });
                        
                        if (!otherFieldsValid) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        
                        form.classList.add('was-validated');
                        
                        // 确保ratio和duration字段永远不显示验证错误
                        setTimeout(function() {
                            if (ratioField) {
                                ratioField.classList.remove('is-invalid', 'is-valid');
                            }
                            if (durationField) {
                                durationField.classList.remove('is-invalid', 'is-valid');
                            }
                        }, 10);
                    }, false)
                })
        })();// 处理取消按钮
        function handleCancel(event) {
            event.preventDefault();
            handleReturn();
            return false;
        }        // 根据比例自动计算额定工期的函数
        function calculateRatedDurationFromRatio() {
            // 获取当前比例值
            var ratio = parseFloat($('#ratio').val()) || 0;
            
            // 获取项目额定工期
            var projectRatedDuration = 0;
            // 从页面中获取项目额定工期数据
            console.log('window.projectRatedDuration原始值:', window.projectRatedDuration);
            console.log('window.projectRatedDuration类型:', typeof window.projectRatedDuration);
            
            if (window.projectRatedDuration !== undefined && window.projectRatedDuration !== null) {
                projectRatedDuration = parseFloat(window.projectRatedDuration) || 0;
                console.log('解析后的项目额定工期:', projectRatedDuration);
            } else {
                // 备用方案：使用默认值0（如果数据传递失败）
                console.warn('无法获取项目额定工期，使用默认值0');
                projectRatedDuration = 0;
            }
            
            // 计算任务额定工期 = 项目额定工期 * 比例
            var taskRatedDuration = projectRatedDuration * ratio;            // 更新额定工期输入框但不触发验证
            const durationField = $('#ratedDurationDays')[0];
            
            if (durationField) {
                var calculatedValue = taskRatedDuration.toFixed(2);
                
                // 直接设置值，不触发事件避免验证问题
                durationField.value = calculatedValue;
                durationField.setAttribute('value', calculatedValue);
                
                // 确保没有验证错误状态
                durationField.setCustomValidity('');
                durationField.classList.remove('is-invalid', 'is-valid');
            }
            
            console.log('计算额定工期: 项目额定工期=' + projectRatedDuration + ', 比例=' + ratio + ', 任务额定工期=' + taskRatedDuration.toFixed(2));
        }        // 根据额定工期反向计算比例的函数
        function calculateRatioFromDuration() {
            // 获取当前额定工期值
            var taskRatedDuration = parseFloat($('#ratedDurationDays').val()) || 0;
            
            // 获取项目额定工期
            var projectRatedDuration = 0;
            if (window.projectRatedDuration !== undefined && window.projectRatedDuration !== null) {
                projectRatedDuration = parseFloat(window.projectRatedDuration) || 0;
            }
            
            // 如果项目额定工期大于0，则计算比例
            if (projectRatedDuration > 0) {
                var ratio = taskRatedDuration / projectRatedDuration;                // 更新比例输入框但不触发验证
                const ratioField = $('#ratio')[0];
                
                if (ratioField) {
                    var calculatedValue = ratio.toFixed(3);
                    
                    // 直接设置值，不触发事件避免验证问题
                    ratioField.value = calculatedValue;
                    ratioField.setAttribute('value', calculatedValue);
                    
                    // 确保没有验证错误状态
                    ratioField.setCustomValidity('');
                    ratioField.classList.remove('is-invalid', 'is-valid');
                }
                
                console.log('计算比例: 任务额定工期=' + taskRatedDuration + ', 项目额定工期=' + projectRatedDuration + ', 比例=' + ratio.toFixed(3));
            } else if (projectRatedDuration <= 0) {
                console.log('项目额定工期未设置或为0，无法计算比例');
            }
        }
    </script>
</body>
</html>