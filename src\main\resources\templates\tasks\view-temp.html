    <!-- 自定义脚本 -->
    <script id="customScript" th:inline="javascript">
        // 由后端注入的审批流程强制开关变量，供本脚本块全局使用
        var isForceApprovalNeeded = /*[[${isForceApprovalNeeded}]]*/ false;
        
        function handleSmartBack() {
            // 获取来源页面参数
            const fromPage = new URLSearchParams(window.location.search).get('from');

            if (fromPage === 'dashboard') {
                window.location.href = '/dashboard';
            } else {
                window.history.back();
            }
        }

        // 设置删除任务对话框数据
        document.getElementById('deleteModal').addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const taskId = button.getAttribute('data-task-id');
            document.getElementById('deleteTaskId').value = taskId;
        });

        // 设置删除子任务评论对话框数据
        document.getElementById('deleteSubTaskModal').addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const subTaskId = button.getAttribute('data-subtask-id');
            const taskId = button.getAttribute('data-task-id');
            document.getElementById('deleteSubTaskId').value = subTaskId;
            document.getElementById('taskIdForSubTask').value = taskId;
        });

        // 设置删除提交记录对话框数据
        const deleteSubmitModal = document.getElementById('deleteSubmitModal');
        if (deleteSubmitModal) {
            deleteSubmitModal.addEventListener('show.bs.modal', function (event) {
                const button = event.relatedTarget;
                const submitId = button.getAttribute('data-submit-id');
                const taskId = button.getAttribute('data-task-id');
                document.getElementById('deleteSubmitId').value = submitId;
                document.getElementById('taskIdForSubmit').value = taskId;
            });
        }
        
        // 文件上传进度条相关代码
        document.addEventListener('DOMContentLoaded', function () {
            const submitForm = document.querySelector('#completeModal form');
            const submitBtn = document.getElementById('submit-task-btn');
            const file1Input = document.getElementById('file1');
            const file2Input = document.getElementById('file2');
            const progressContainer1 = document.getElementById('progress-container-1');
            const progressContainer2 = document.getElementById('progress-container-2');
            const progressBar1 = document.getElementById('progress-bar-1');
            const progressBar2 = document.getElementById('progress-bar-2');
            const needApprovalCheckbox = document.getElementById('needApproval');

            // 检查是否为强制审批任务
            if (isForceApprovalNeeded) {
                needApprovalCheckbox.checked = true;
                needApprovalCheckbox.disabled = true;
            }

            // 任务状态选择相关
            const statusCompletedRadio = document.getElementById('statusCompleted');
            const statusPausedRadio = document.getElementById('statusPaused');
            const completedAlert = document.getElementById('completedAlert');
            const pausedAlert = document.getElementById('pausedAlert');

            // 监听状态选择变化
            function updateStatusAlert() {
                if (statusCompletedRadio.checked) {
                    completedAlert.classList.remove('d-none');
                    pausedAlert.classList.add('d-none');
                } else {
                    completedAlert.classList.add('d-none');
                    pausedAlert.classList.remove('d-none');
                }
            }

            statusCompletedRadio.addEventListener('change', updateStatusAlert);
            statusPausedRadio.addEventListener('change', updateStatusAlert);

            // 文件上传状态跟踪
            let file1Uploaded = true; // 默认为true，因为文件1可能不需要上传
            let file2Uploaded = true; // 默认为true，因为文件2可能不需要上传

            // 检查是否可以提交
            function checkSubmitEnabled() {
                submitBtn.disabled = !(file1Uploaded && file2Uploaded);
            }

            // 初始化 Bootstrap 表单验证
            submitForm.addEventListener('submit', function (event) {
                if (!submitForm.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                submitForm.classList.add('was-validated');
            });

            // 提交按钮点击处理
            submitBtn.addEventListener('click', async function (event) {
                event.preventDefault();

                if (!submitForm.checkValidity()) {
                    submitForm.classList.add('was-validated');
                    return;
                }

                const formData = new FormData();
                const taskId = document.getElementById('taskId').value;
                const remarks = document.getElementById('remarks').value;
                const submitName = document.getElementById('submitName').value;
                const needApproval = document.getElementById('needApproval').checked;
                const taskCompletionStatus = document.querySelector('input[name="taskCompletionStatus"]:checked').value;

                formData.append('taskId', taskId);
                formData.append('remarks', remarks);
                formData.append('submitName', submitName);

                if (file1Input.files.length > 0) {
                    formData.append('file1', file1Input.files[0]);
                }
                if (file2Input.files.length > 0) {
                    formData.append('file2', file2Input.files[0]);
                }

                try {
                    // 禁用提交按钮
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 提交中...';

                    const response = await fetch('/submits2/save', {
                        method: 'POST',
                        body: formData
                    });

                    if (response.ok) {
                        const modalInstance = bootstrap.Modal.getInstance(document.getElementById('completeModal'));
                        modalInstance.hide();

                        if (needApproval) {
                            // 提交成功后如果需要审批，则跳转到发起流程页面
                            window.location.href = `/workflow/start?businessId=${taskId}&businessType=任务`;
                        } else {
                            // 否则刷新当前页面
                            window.location.reload();
                        }
                    } else {
                        throw new Error('提交失败');
                    }
                } catch (error) {
                    console.error('提交出错:', error);
                    alert('提交失败，请重试');
                    // 恢复提交按钮状态
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '提交';
                }
            });

            // 监听文件1的选择
            file1Input.addEventListener('change', function () {
                if (file1Input.files.length > 0) {
                    progressContainer1.classList.remove('d-none');
                    // 这里可以添加文件上传进度模拟
                    progressBar1.style.width = '100%';
                    progressBar1.textContent = '100%';
                } else {
                    progressContainer1.classList.add('d-none');
                }
            });

            // 监听文件2的选择
            file2Input.addEventListener('change', function () {
                if (file2Input.files.length > 0) {
                    progressContainer2.classList.remove('d-none');
                    // 这里可以添加文件上传进度模拟
                    progressBar2.style.width = '100%';
                    progressBar2.textContent = '100%';
                } else {
                    progressContainer2.classList.add('d-none');
                }
            });
        });
    </script>
