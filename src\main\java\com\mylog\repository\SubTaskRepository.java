package com.mylog.repository;

import com.mylog.model.SubTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface SubTaskRepository extends JpaRepository<SubTask, Long> {
    
    List<SubTask> findByTaskId(Long taskId);
    
    List<SubTask> findByTaskIdOrderBySequenceNumberAsc(Long taskId);
    
    @Query(value = "SELECT * FROM SubTasks s WHERE s.taskId = :taskId ORDER BY s.CreatedDate DESC", nativeQuery = true)
    List<SubTask> querySubTasksByTaskId(@Param("taskId") Long taskId);
    
    @Query(value = "SELECT * FROM SubTasks s WHERE s.taskId = :taskId ORDER BY s.CreatedDate DESC", 
           countQuery = "SELECT COUNT(*) FROM SubTasks s WHERE s.taskId = :taskId",
           nativeQuery = true)
    Page<SubTask> querySubTasksByTaskIdPaged(@Param("taskId") Long taskId, Pageable pageable);
    
    @Query("SELECT MAX(s.sequenceNumber) FROM SubTask s WHERE s.taskId = :taskId")
    Integer findMaxSequenceNumberByTaskId(@Param("taskId") Long taskId);
    
    @Query(value = "SELECT * FROM SubTasks s WHERE s.CreatedDate BETWEEN :startDate AND :endDate", nativeQuery = true)
    List<SubTask> findByCreatedDateBetween(
            @Param("startDate") String startDate, 
            @Param("endDate") String endDate);
    
    @Query("SELECT s FROM SubTask s WHERE s.logContent LIKE %:keyword%")
    List<SubTask> findByLogContentContaining(@Param("keyword") String keyword);
    
    @Query(value = "SELECT * FROM SubTasks s WHERE s.taskId = :taskId ORDER BY s.CreatedDate DESC LIMIT 1", nativeQuery = true)
    Optional<SubTask> queryLatestSubTaskByTaskId(@Param("taskId") Long taskId);
    
    /**
     * 批量获取多个任务的最新子任务
     * @param taskIds 任务ID列表
     * @return 每个任务的最新子任务
     */
    @Query(value = "SELECT s1.* FROM SubTasks s1 " +
                   "JOIN (SELECT taskId, MAX(CreatedDate) as maxDate FROM SubTasks WHERE taskId IN :taskIds GROUP BY taskId) s2 " +
                   "ON s1.taskId = s2.taskId AND s1.CreatedDate = s2.maxDate", 
           nativeQuery = true)
    List<SubTask> findLatestSubTasksByTaskIds(@Param("taskIds") List<Long> taskIds);
} 