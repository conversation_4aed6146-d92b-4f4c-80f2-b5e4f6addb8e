<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('流程审批')}">
    <meta charset="UTF-8">
    <title>流程审批</title>
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::#extraScripts})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">
                流程审批
                <small class="text-muted" th:text="'- ' + ${instance.title}"></small>
            </h1>            <div>
                <button type="button" class="btn btn-outline-secondary me-2" onclick="cancelAndCleanup()">
                    <i class="bi bi-arrow-left"></i> 返回
                </button>
            </div>
        </div>

        <!-- 错误消息 -->
        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <span th:text="${error}">操作失败</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>        <!-- 流程信息卡片 - 使用公共片段 -->
        <div class="row mb-4">
            <div class="col-md-12">                <!-- 引用共享的信息卡片片段 -->
                <div th:replace="~{workflow/fragments/workflow-info-card :: info-card(${instance}, ${currentStep}, ${totalSteps}, ${stepLabels}, ${stepComments}, ${stepTimes})}"></div>
            </div>
        </div>

        <!-- 审批表单卡片 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">审批操作</h5>
                    </div>
                    <div class="card-body">
                        <!-- 草稿状态：提交表单 -->
                        <div th:if="${instance.status.name() == 'DRAFT'}">
                            <form id="submit-form" th:action="@{/workflow/approval/submit}" method="post" enctype="multipart/form-data">
                                <input type="hidden" name="instanceId" th:value="${instance.instanceId}">
                                <input type="hidden" id="tempFilePath1" name="tempFilePath1">
                                <input type="hidden" id="tempFilePath2" name="tempFilePath2">
                                <input type="hidden" id="fileName1" name="fileName1">
                                <input type="hidden" id="fileName2" name="fileName2">                                <div class="mb-3">
                                    <label for="comment" class="form-label">提交说明</label>
                                    <textarea class="form-control" id="comment" name="comment" rows="3" required>请审批</textarea>
                                </div>
                                
                                <!-- 添加：指定第一步审批人（当第一步需要动态指定审批人时显示） -->
                                <div class="mb-3" id="firstApproverDiv" th:style="${firstStepNeedsDynamicApprover ? '' : 'display: none;'}">
                                    <label for="nextApprover" class="form-label">指定审批人 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="nextApprover" name="nextApprover" th:required="${firstStepNeedsDynamicApprover}" 
                                            th:data-required="${firstStepNeedsDynamicApprover}">                                        <option value="" selected disabled>请选择审批人</option>
                                        <!-- 使用后端传递的personnel数据，添加安全检查 -->
                                        <option th:each="person : ${personnel}" 
                                                th:if="${person != null and !#strings.isEmpty(person)}" 
                                                th:value="${person}" 
                                                th:text="${person}"></option>
                                    </select>
                                    <div class="form-text">请指定本流程的第一步审批人</div>
                                    <div class="invalid-feedback">必须选择一个审批人</div>
                                </div>

                                <!-- <div class="mb-3">
                                    <label for="file1" class="form-label">附件1</label>
                                    <input type="file" class="form-control" id="file1" name="file1">
                                    <div id="progress-container-1" class="progress mt-2 d-none">
                                        <div id="progress-bar-1" class="progress-bar progress-bar-striped progress-bar-animated"
                                             role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">0%</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="file2" class="form-label">附件2</label>
                                    <input type="file" class="form-control" id="file2" name="file2">
                                    <div id="progress-container-2" class="progress mt-2 d-none">
                                        <div id="progress-bar-2" class="progress-bar progress-bar-striped progress-bar-animated"
                                             role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">0%</div>
                                    </div>
                                </div> -->                                <div class="text-end">
                                    <button type="button" class="btn btn-secondary me-2" onclick="cancelAndCleanup()">取消</button>
                                    <button type="button" id="submit-btn" class="btn btn-primary">提交流程</button>
                                </div>
                            </form>
                        </div>

                        <!-- 处理中状态且是发起人且当前审批步骤为第1步：撤回表单 -->
                        <div th:if="${instance.status.name() == 'PROCESSING' && instance.initiator == #authentication.name && currentStep == 1}">
                            <form th:action="@{/workflow/approval/withdraw}" method="post">
                                <input type="hidden" name="instanceId" th:value="${instance.instanceId}">

                                <div class="mb-3">
                                    <label for="withdrawComment" class="form-label">撤回原因</label>
                                    <textarea class="form-control" id="withdrawComment" name="comment" rows="3" required></textarea>
                                </div>                                <div class="text-end">
                                    <button type="button" class="btn btn-secondary me-2" onclick="cancelAndCleanup()">取消</button>
                                    <button type="submit" class="btn btn-warning">撤回流程</button>
                                </div>
                            </form>
                        </div>
                        
                        <!-- 处理中状态：审批表单 -->
                        <div th:if="${  (instance.status.name() == 'PROCESSING'  ) }">
                            <ul class="nav nav-tabs mb-3" id="approvalTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="approve-tab" data-bs-toggle="tab" data-bs-target="#approve" type="button" role="tab" aria-controls="approve" aria-selected="true">同意</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="reject-tab" data-bs-toggle="tab" data-bs-target="#reject" type="button" role="tab" aria-controls="reject" aria-selected="false">拒绝</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="transfer-tab" data-bs-toggle="tab" data-bs-target="#transfer" type="button" role="tab" aria-controls="transfer" aria-selected="false">转交</button>
                                </li>
                            </ul>
                            <div class="tab-content" id="approvalTabContent">
                                <!-- 同意表单 -->
                                <div class="tab-pane fade show active" id="approve" role="tabpanel" aria-labelledby="approve-tab">
                                    <form id="approve-form" th:action="@{/workflow/approval/approve}" method="post" enctype="multipart/form-data">
                                        <input type="hidden" name="instanceId" th:value="${instance.instanceId}">                                        <input type="hidden" id="tempFilePath1" name="tempFilePath1">
                                        <input type="hidden" id="tempFilePath2" name="tempFilePath2">
                                        <input type="hidden" id="fileName1" name="fileName1">
                                        <input type="hidden" id="fileName2" name="fileName2">

                                        <div class="mb-3">
                                            <label for="approveComment" class="form-label">审批意见</label>
                                            <textarea class="form-control" id="approveComment" name="comment" rows="3" required>同意</textarea>
                                        </div>
                                           
                                        <!-- 任务完成状态开关（仅在任务类型的最后审批步骤时显示） -->
                                        <div class="mb-3" id="taskCompletionDiv" th:style="${isTaskLastStep ? '' : 'display: none;'}">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="completeTaskAfterApproval" name="completeTaskAfterApproval" checked>
                                                <label class="form-check-label" for="completeTaskAfterApproval">
                                                    审批后任务状态切换到完成
                                                </label>
                                                <div class="form-text">选中时任务状态将设为"已完成"，不选中时任务状态为"进行中"</div>
                                            </div>
                                        </div>
                                        
                                        <!-- 添加：指定下一步审批人（当下一步需要动态指定审批人时显示） -->
                                        <div class="mb-3" id="nextApproverDiv" th:style="${nextStepNeedsDynamicApprover ? '' : 'display: none;'}">
                                            <label for="nextApprover" class="form-label">指定下一步审批人</label>
                                            <select class="form-select" id="nextApprover" name="nextApprover" th:required="${nextStepNeedsDynamicApprover}">
                                                <option value="" selected disabled>请选择下一步审批人</option>                                                <!-- 使用后端传递的personnel数据 - 正确处理字符串列表 -->
                                                <option th:each="person : ${personnel}" 
                                                        th:if="${person != null and !#strings.isEmpty(person)}" 
                                                        th:value="${person}" 
                                                        th:text="${person}"></option>
                                            </select>
                                            <div class="form-text">如果下一步审批人类型为"动态指定"，请在此选择具体的审批人</div>
                                        </div>                                        <div class="mb-3">
                                            <label for="file1" class="form-label">文件1</label>
                                            <input type="file" class="form-control" id="file1" name="file1">
                                            <div id="progress-container-1" class="progress mt-2 d-none">
                                                <div id="progress-bar-1" class="progress-bar progress-bar-striped progress-bar-animated"
                                                     role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">0%</div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="file2" class="form-label">文件2</label>
                                            <input type="file" class="form-control" id="file2" name="file2">
                                            <div id="progress-container-2" class="progress mt-2 d-none">
                                                <div id="progress-bar-2" class="progress-bar progress-bar-striped progress-bar-animated"
                                                     role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">0%</div>
                                            </div>
                                        </div>                                        <div class="text-end">
                                            <button type="button" class="btn btn-secondary me-2" onclick="cancelAndCleanup()">取消</button>
                                            <button type="button" id="approve-btn" class="btn btn-success">同意</button>
                                        </div>
                                    </form>
                                </div>                                <!-- 拒绝表单 -->
                                <div class="tab-pane fade" id="reject" role="tabpanel" aria-labelledby="reject-tab">
                                    <form id="reject-form" th:action="@{/workflow/approval/reject}" method="post">
                                        <input type="hidden" name="instanceId" th:value="${instance.instanceId}">

                                        <div class="mb-3">
                                            <label for="rejectComment" class="form-label">拒绝原因</label>
                                            <textarea class="form-control" id="rejectComment" name="comment" rows="3" required></textarea>
                                        </div>

                                        <div class="text-end">
                                            <button type="button" class="btn btn-secondary me-2" onclick="cancelAndCleanup()">取消</button>
                                            <button type="button" id="reject-btn" class="btn btn-danger">拒绝</button>
                                        </div>
                                    </form>
                                </div>

                                <!-- 转交表单 -->
                                <div class="tab-pane fade" id="transfer" role="tabpanel" aria-labelledby="transfer-tab">
                                    <form th:action="@{/workflow/approval/transfer}" method="post">
                                        <input type="hidden" name="instanceId" th:value="${instance.instanceId}">

                                        <div class="mb-3">
                                            <label for="toUsername" class="form-label">转交给</label>
                                            <select class="form-select" id="toUsername" name="toUsername" required>
                                                <option value="" selected disabled>请选择转交人员</option>                                                <!-- 使用后端传递的personnel数据 -->
                                                <option th:each="person : ${personnel}" 
                                                        th:if="${person != null and !#strings.isEmpty(person)}" 
                                                        th:value="${person}" 
                                                        th:text="${person}"></option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="transferComment" class="form-label">转交说明</label>
                                            <textarea class="form-control" id="transferComment" name="comment" rows="3" required></textarea>
                                        </div>                                        <div class="text-end">
                                            <button type="button" class="btn btn-secondary me-2" onclick="cancelAndCleanup()">取消</button>
                                            <button type="submit" class="btn btn-warning">转交</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>


                        <!-- 其他状态：不可操作 -->
                        <div th:if="${instance.status.name() != 'DRAFT' && instance.status.name() != 'PROCESSING'}">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                当前流程状态不允许进行审批操作
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="extraScripts">
        <!-- 引入 Select2 CSS -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
        
        <!-- 引入 Select2 JS -->
        <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
        
        <!-- 引入工作流信息卡片处理脚本 -->
        <script th:src="@{/js/workflow/instance-form.js}"></script>          <script th:inline="javascript">
            // 立即执行的保护代码，在任何其他代码执行之前运行
            (function() {
                // 全局JavaScript错误处理
                window.addEventListener('error', function(e) {
                    console.error('全局JavaScript错误:', e.error);
                    console.error('错误消息:', e.message);
                    console.error('发生位置:', e.filename + ':' + e.lineno + ':' + e.colno);
                    if (e.error && e.error.stack) {
                        console.error('错误堆栈:', e.error.stack);
                    }
                });
                
                // 捕获Promise未处理的拒绝
                window.addEventListener('unhandledrejection', function(e) {
                    console.error('未处理的Promise拒绝:', e.reason);
                });
                
                // 保存原始方法
                var originalToLowerCase = String.prototype.toLowerCase;
                var originalGetAttribute = Element.prototype.getAttribute;
                var originalSetAttribute = Element.prototype.setAttribute;
                
                // 重写String.prototype.toLowerCase
                String.prototype.toLowerCase = function() {
                    try {
                        if (this === null || this === undefined) {
                            console.warn('toLowerCase called on null/undefined, returning empty string');
                            return '';
                        }
                        return originalToLowerCase.call(this);
                    } catch (error) {
                        console.error('toLowerCase error:', error, 'this:', this);
                        return '';
                    }
                };
                
                // 重写getAttribute以防止null/undefined问题
                Element.prototype.getAttribute = function(name) {
                    try {
                        var result = originalGetAttribute.call(this, name);
                        return result === null ? null : result; // 保持null值，但确保安全
                    } catch (error) {
                        console.error('getAttribute error:', error, 'name:', name);
                        return null;
                    }
                };
                
                // 重写setAttribute以防止null/undefined问题
                Element.prototype.setAttribute = function(name, value) {
                    try {
                        if (value === null || value === undefined) {
                            value = '';
                        }
                        return originalSetAttribute.call(this, name, String(value));
                    } catch (error) {
                        console.error('setAttribute error:', error, 'name:', name, 'value:', value);
                    }
                };
                
                console.log('JavaScript安全保护已启用');
            })();
            
            // 工具函数：安全获取字符串的小写形式
            function safeToLowerCase(str) {
                try {
                    if (str === null || str === undefined) {
                        return '';
                    }
                    if (typeof str !== 'string') {
                        str = String(str);
                    }
                    return str.toLowerCase();
                } catch (error) {
                    console.error('safeToLowerCase error:', error, 'input:', str);
                    return '';
                }
            }
              // 获取Thymeleaf传递的人员数据(直接显示为JavaScript变量)
            var personnelData = /*[[${personnel}]]*/ [];
            
            // 获取是否需要动态指定下一步审批人的标志
            var nextStepNeedsDynamicApprover = /*[[${nextStepNeedsDynamicApprover}]]*/ false;
            
            // DOM加载完成后的立即安全检查
            $(document).ready(function() {
                console.log("=== DOM加载完成，开始安全检查 ===");
                
                // 立即检查所有select元素的安全性
                $("select").each(function() {
                    var $select = $(this);
                    var selectId = $select.attr("id") || "未知ID";
                    
                    $select.find('option').each(function() {
                        var $option = $(this);
                        var value = $option.val();
                        var text = $option.text();
                        
                        // 修复null/undefined值
                        if (value === null || value === undefined) {
                            console.warn('修复select选项null值:', selectId, $option.index());
                            $option.val('');
                        }
                        if (text === null || text === undefined) {
                            console.warn('修复select选项null文本:', selectId, $option.index());
                            $option.text('');
                        }
                        
                        // 确保字符串类型
                        if (typeof value !== 'string' && value !== '') {
                            $option.val(String(value || ''));
                        }
                        if (typeof text !== 'string' && text !== '') {
                            $option.text(String(text || ''));
                        }
                    });
                });
                
                console.log("=== Select元素安全检查完成 ===");
            });
              $(document).ready(function() {                console.log("=== 审批表单初始化开始 ===");
                console.log("是否需要动态指定下一步审批人:", nextStepNeedsDynamicApprover);                // 全局取消并清理函数
                window.cancelAndCleanup = function() {
                    console.log("=== 执行取消操作，开始清理临时文件 ===");
                    
                    // 收集所有需要删除的临时文件路径
                    const tempFilePaths = [];
                    
                    // 检查同意表单的临时文件
                    const tempFilePath1 = $("#tempFilePath1").val();
                    const tempFilePath2 = $("#tempFilePath2").val();
                    
                    console.log("检查同意表单文件:");
                    console.log("  tempFilePath1:", tempFilePath1);
                    console.log("  tempFilePath2:", tempFilePath2);
                    
                    if (tempFilePath1 && tempFilePath1.trim() !== '') {
                        tempFilePaths.push(tempFilePath1.trim());
                        console.log("  添加文件1:", tempFilePath1.trim());
                    }                    if (tempFilePath2 && tempFilePath2.trim() !== '') {
                        tempFilePaths.push(tempFilePath2.trim());
                        console.log("  添加文件2:", tempFilePath2.trim());
                    }
                    
                    console.log("=== 最终需要删除的临时文件列表 ===");
                    console.log("文件数量:", tempFilePaths.length);
                    console.log("文件列表:", tempFilePaths);
                    
                    // 如果没有临时文件需要删除，直接跳转
                    if (tempFilePaths.length === 0) {
                        console.log("没有临时文件需要删除，直接跳转");
                        window.location.href = '/workflow/instances/' + /*[[${instance.instanceId}]]*/ '';
                        return;
                    }
                    
                    // 显示清理进度提示
                    console.log("开始清理 " + tempFilePaths.length + " 个临时文件...");
                    
                    // 删除临时文件
                    deleteTempFiles(tempFilePaths, function() {
                        console.log("=== 临时文件清理完成，跳转到流程详情页 ===");
                        window.location.href = '/workflow/instances/' + /*[[${instance.instanceId}]]*/ '';
                    });
                };
                  // 删除临时文件的函数（增强版调试）
                function deleteTempFiles(filePaths, callback) {
                    console.log("=== deleteTempFiles 函数开始执行 ===");
                    console.log("传入的文件路径数组:", filePaths);
                    console.log("传入的回调函数:", typeof callback);
                    
                    if (!filePaths || filePaths.length === 0) {
                        console.log("文件路径数组为空，直接执行回调");
                        if (callback) callback();
                        return;
                    }
                    
                    let deletedCount = 0;
                    const totalFiles = filePaths.length;
                    console.log("需要删除的文件总数:", totalFiles);
                    
                    // 获取CSRF token
                    const token = $("meta[name='_csrf']").attr("content");
                    const header = $("meta[name='_csrf_header']").attr("content");
                    
                    console.log("CSRF配置:");
                    console.log("  token:", token);
                    console.log("  header:", header);
                    
                    // 设置超时，确保回调函数最终会被调用
                    const timeoutId = setTimeout(function() {
                        console.warn('⚠️ 删除临时文件超时(5秒)，强制执行回调');
                        if (callback) callback();
                    }, 5000); // 5秒超时
                    
                    filePaths.forEach(function(filePath, index) {
                        console.log(`\n--- 开始处理文件 ${index + 1}/${totalFiles} ---`);
                        console.log("文件路径:", filePath);
                          // 首先尝试DELETE请求
                        console.log("🔄 发送DELETE请求...");
                        $.ajax({
                            url: '/api/files/delete-temp',
                            type: 'DELETE',
                            data: { filePath: filePath },
                            timeout: 3000,
                            dataType: 'json',
                            headers: {
                                'Accept': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            beforeSend: function(xhr) {
                                console.log("设置DELETE请求头...");
                                if (header && token) {
                                    xhr.setRequestHeader(header, token);
                                    console.log("✅ CSRF头已设置");
                                } else {
                                    console.log("⚠️ 缺少CSRF token或header");
                                }
                            },
                            success: function(response) {
                                console.log(`✅ DELETE成功 (${index + 1}/${totalFiles}):`, filePath);
                                console.log("响应:", response);
                                deletedCount++;
                                console.log(`进度: ${deletedCount}/${totalFiles}`);
                                if (deletedCount >= totalFiles) {
                                    clearTimeout(timeoutId);
                                    console.log("🎉 所有文件删除完成，执行回调");
                                    if (callback) callback();
                                }
                            },
                            error: function(xhr, status, error) {
                                console.warn(`❌ DELETE失败 (${index + 1}/${totalFiles}):`, filePath);
                                console.log("状态:", status, "错误:", error);
                                console.log("响应状态码:", xhr.status);
                                console.log("响应文本:", xhr.responseText);
                                
                                console.log("🔄 尝试POST请求作为备选...");
                                  // DELETE失败时，尝试POST请求
                                $.ajax({
                                    url: '/api/files/delete-temp',
                                    type: 'POST',
                                    data: { filePath: filePath },
                                    timeout: 3000,
                                    dataType: 'json',
                                    headers: {
                                        'Accept': 'application/json',
                                        'X-Requested-With': 'XMLHttpRequest'
                                    },
                                    beforeSend: function(xhr) {
                                        console.log("设置POST请求头...");
                                        if (header && token) {
                                            xhr.setRequestHeader(header, token);
                                            console.log("✅ POST CSRF头已设置");
                                        }
                                    },
                                    success: function(response) {
                                        console.log(`✅ POST成功 (${index + 1}/${totalFiles}):`, filePath);
                                        console.log("POST响应:", response);
                                    },
                                    error: function(xhr, status, error) {
                                        console.error(`❌ POST也失败 (${index + 1}/${totalFiles}):`, filePath);
                                        console.log("POST状态:", status, "POST错误:", error);
                                        console.log("POST响应状态码:", xhr.status);
                                        console.log("POST响应文本:", xhr.responseText);
                                    },
                                    complete: function() {
                                        deletedCount++;
                                        console.log(`POST完成，进度: ${deletedCount}/${totalFiles}`);
                                        if (deletedCount >= totalFiles) {
                                            clearTimeout(timeoutId);
                                            console.log("🎉 所有文件处理完成，执行回调");
                                            if (callback) callback();
                                        }
                                    }
                                });
                            }
                        });
                    });
                    
                    console.log("=== deleteTempFiles 函数设置完成，等待异步请求结果 ===");
                }
                  // 安全检查人员数据
                if (!personnelData || !Array.isArray(personnelData)) {
                    console.warn("人员数据为空或格式不正确，使用空数组", personnelData);
                    personnelData = [];
                } else {
                    // 确保人员数据中的每个元素都是字符串且不为空
                    personnelData = personnelData.filter(function(person) {
                        return person && typeof person === 'string' && person.trim().length > 0;
                    });
                    console.log("过滤后的人员数据:", personnelData);
                }
                
                // 文件上传状态跟踪变量 - 将变量声明移到最前面，并确保初始值为false
                var file1Uploading = false;
                var file2Uploading = false;
                
                // 文件上传状态跟踪，默认为true表示不需要上传
                var file1Uploaded = true;
                var file2Uploaded = true;
                
                // 检查是否可以提交表单
                function checkSubmitEnabled() {
                    // 只有当两个文件都已上传完成(或者不需要上传)时，才能提交
                    if (file1Uploaded && file2Uploaded) {
                        return true;
                    }
                    return false;
                }
                
                // 在页面加载时强制确保变量为false
                function resetUploadStatus() {
                    console.log("重置文件上传状态...");
                    file1Uploading = false;
                    file2Uploading = false;
                    file1Uploaded = true;
                    file2Uploaded = true;
                }
                
                // 页面加载时立即重置上传状态
                resetUploadStatus();
                
                // 提交流程按钮点击事件处理
                $("#submit-btn").on("click", function() {
                    console.log("提交流程按钮被点击");
                    
                    // 校验表单
                    var form = $("#submit-form")[0];
                    if (!form.checkValidity()) {
                        console.log("表单验证失败");
                        // 添加was-validated类以显示验证消息
                        $(form).addClass("was-validated");
                        return;
                    }
                    
                    // 如果第一步需要动态指定审批人，检查是否已选择
                    var firstApproverDiv = $("#firstApproverDiv");
                    if (firstApproverDiv.is(":visible")) {
                        var nextApprover = $("#nextApprover").val();
                        if (!nextApprover) {
                            alert("请选择审批人");
                            return;
                        }
                    }
                    
                    // 检查是否有文件正在上传
                    if (!checkSubmitEnabled()) {
                        console.log("文件正在上传，不能提交表单");
                        alert("请等待文件上传完成后再提交！");
                        return;
                    }
                    
                    // 提交表单
                    console.log("提交表单...");
                    form.submit();
                });
                  // 处理附件1上传
                $("#file1").on("change", function() {
                    var file = this.files[0];
                    if (!file) {
                        file1Uploaded = true;
                        return;
                    }
                    
                    // 检查文件大小
                    const fileSize = file.size;
                    const maxSize = 50 * 1024 * 1024; // 50MB
                    
                    if (fileSize > maxSize) {
                        console.error('文件1大小超出限制:', fileSize, '最大允许:', maxSize);
                        var progressBar = $("#progress-bar-1");
                        progressBar.addClass("bg-danger");
                        progressBar.width("100%");
                        progressBar.attr("aria-valuenow", 100);
                        progressBar.text("文件过大");
                        alert("文件大小不能超过50MB！");
                        return;
                    }
                    
                    // 显示进度条并设置状态
                    $("#progress-container-1").removeClass("d-none");
                    var progressBar = $("#progress-bar-1");
                    progressBar.width("0%");
                    progressBar.attr("aria-valuenow", 0);
                    progressBar.text("0%");
                    progressBar.removeClass("bg-success bg-danger");
                    progressBar.addClass("progress-bar-striped progress-bar-animated");
                      file1Uploading = true;
                    file1Uploaded = false;
                    console.log("附件1开始上传，设置file1Uploading =", file1Uploading);
                    
                    // 创建FormData对象
                    var formData = new FormData();
                    formData.append("file", file);
                    
                    // 获取CSRF token
                    var token = $("meta[name='_csrf']").attr("content");
                    var header = $("meta[name='_csrf_header']").attr("content");
                    
                    // 发送上传请求
                    $.ajax({
                        url: "/api/files/upload-temp",
                        type: "POST",
                        data: formData,
                        contentType: false,
                        processData: false,
                        dataType: 'json', // 明确指定期望的响应类型
                        beforeSend: function(xhr) {
                            // 只有当header和token都存在时才设置请求头
                            if (header && token) {
                                xhr.setRequestHeader(header, token);
                            }
                        },
                        xhr: function() {
                            var xhr = new window.XMLHttpRequest();
                            xhr.upload.addEventListener("progress", function(evt) {
                                if (evt.lengthComputable) {
                                    var percentComplete = Math.round((evt.loaded / evt.total) * 100);
                                    var progressBar = $("#progress-bar-1");
                                    progressBar.width(percentComplete + "%");
                                    progressBar.attr("aria-valuenow", percentComplete);
                                    progressBar.text(percentComplete + "%");
                                }
                            }, false);
                            return xhr;
                        },
                        success: function(data) {
                            file1Uploading = false;
                            console.log("附件1上传成功响应:", data);
                            
                            var progressBar = $("#progress-bar-1");                            if (data.success) {                                // 更新同意表单的隐藏字段
                                $("#tempFilePath1").val(data.tempFilePath || "");
                                $("#fileName1").val(file.name || "");
                                
                                // 更新进度条样式
                                progressBar.removeClass("progress-bar-striped progress-bar-animated");
                                progressBar.addClass("bg-success");
                                progressBar.attr("aria-valuenow", 100);
                                progressBar.width("100%");
                                progressBar.text("上传成功");
                                
                                // 标记上传完成
                                file1Uploaded = true;
                            } else {
                                progressBar.removeClass("progress-bar-striped progress-bar-animated");
                                progressBar.addClass("bg-danger");
                                progressBar.attr("aria-valuenow", 100);
                                progressBar.width("100%");
                                progressBar.text("上传失败: " + (data.message || "未知错误"));
                                
                                // 上传失败也标记为完成，避免无法提交表单
                                file1Uploaded = true;
                            }
                            
                            console.log("附件1上传完成，状态更新为: file1Uploading =", file1Uploading, 
                                ", file1Uploaded =", file1Uploaded);
                        },                        error: function(xhr, status, error) {
                            file1Uploading = false;
                            var progressBar = $("#progress-bar-1");
                            
                            progressBar.removeClass("progress-bar-striped progress-bar-animated");
                            progressBar.addClass("bg-danger");
                            progressBar.text("上传错误: " + (error || "网络错误"));
                            
                            // 上传失败时不将file1Uploaded设为true
                            file1Uploaded = false;
                            
                            console.log("附件1上传错误，状态更新为: file1Uploading =", file1Uploading, 
                                ", file1Uploaded =", file1Uploaded);
                        }
                    });
                });                // 处理附件2上传
                $("#file2").on("change", function() {
                    var file = this.files[0];
                    if (!file) {
                        file2Uploaded = true;
                        return;
                    }
                    
                    // 检查文件大小
                    const fileSize = file.size;
                    const maxSize = 50 * 1024 * 1024; // 50MB
                    
                    if (fileSize > maxSize) {
                        console.error('文件2大小超出限制:', fileSize, '最大允许:', maxSize);
                        var progressBar = $("#progress-bar-2");
                        progressBar.addClass("bg-danger");
                        progressBar.width("100%");
                        progressBar.attr("aria-valuenow", 100);
                        progressBar.text("文件过大");
                        alert("文件大小不能超过50MB！");
                        return;
                    }
                    
                    // 显示进度条并设置状态
                    $("#progress-container-2").removeClass("d-none");
                    var progressBar = $("#progress-bar-2");
                    progressBar.width("0%");
                    progressBar.attr("aria-valuenow", 0);
                    progressBar.text("0%");
                    progressBar.removeClass("bg-success bg-danger");
                    progressBar.addClass("progress-bar-striped progress-bar-animated");
                      file2Uploading = true;
                    file2Uploaded = false;
                    console.log("附件2开始上传，设置file2Uploading =", file2Uploading);
                    
                    // 创建FormData对象
                    var formData = new FormData();
                    formData.append("file", file);
                    
                    // 获取CSRF token
                    var token = $("meta[name='_csrf']").attr("content");
                    var header = $("meta[name='_csrf_header']").attr("content");
                    
                    // 发送上传请求
                    $.ajax({
                        url: "/api/files/upload-temp",
                        type: "POST",
                        data: formData,
                        contentType: false,
                        processData: false,
                        dataType: 'json', // 明确指定期望的响应类型
                        beforeSend: function(xhr) {
                            // 只有当header和token都存在时才设置请求头
                            if (header && token) {
                                xhr.setRequestHeader(header, token);
                            }
                        },
                        xhr: function() {
                            var xhr = new window.XMLHttpRequest();
                            xhr.upload.addEventListener("progress", function(evt) {
                                if (evt.lengthComputable) {
                                    var percentComplete = Math.round((evt.loaded / evt.total) * 100);
                                    var progressBar = $("#progress-bar-2");
                                    progressBar.width(percentComplete + "%");
                                    progressBar.attr("aria-valuenow", percentComplete);
                                    progressBar.text(percentComplete + "%");
                                }
                            }, false);
                            return xhr;
                        },
                        success: function(data) {
                            file2Uploading = false;
                            console.log("附件2上传成功响应:", data);
                            
                            var progressBar = $("#progress-bar-2");                            if (data.success) {                                // 更新同意表单的隐藏字段
                                $("#tempFilePath2").val(data.tempFilePath || "");
                                $("#fileName2").val(file.name || "");
                                
                                // 更新进度条样式
                                progressBar.removeClass("progress-bar-striped progress-bar-animated");
                                progressBar.addClass("bg-success");
                                progressBar.attr("aria-valuenow", 100);
                                progressBar.width("100%");
                                progressBar.text("上传成功");
                                
                                // 标记上传完成
                                file2Uploaded = true;
                            } else {
                                progressBar.removeClass("progress-bar-striped progress-bar-animated");
                                progressBar.addClass("bg-danger");
                                progressBar.attr("aria-valuenow", 100);
                                progressBar.width("100%");
                                progressBar.text("上传失败: " + (data.message || "未知错误"));
                                
                                // 上传失败也标记为完成，避免无法提交表单
                                file2Uploaded = true;
                            }
                            
                            console.log("附件2上传完成，状态更新为: file2Uploading =", file2Uploading, 
                                ", file2Uploaded =", file2Uploaded);
                        },                        error: function(xhr, status, error) {
                            file2Uploading = false;
                            var progressBar = $("#progress-bar-2");
                            
                            progressBar.removeClass("progress-bar-striped progress-bar-animated");
                            progressBar.addClass("bg-danger");
                            progressBar.text("上传错误: " + (error || "网络错误"));
                            
                            // 上传失败时不将file2Uploaded设为true
                            file2Uploaded = false;
                            
                            console.log("附件2上传错误，状态更新为: file2Uploading =", file2Uploading, 
                                ", file2Uploaded =", file2Uploaded);
                        }
                    });                });
                
                // 同样为approve-btn和reject-btn添加点击事件处理
                $("#approve-btn").on("click", function() {
                    console.log("审批通过按钮被点击");
                    
                    // 校验表单
                    var form = $("#approve-form")[0];
                    if (!form.checkValidity()) {
                        console.log("表单验证失败");
                        // 添加was-validated类以显示验证消息
                        $(form).addClass("was-validated");
                        return;
                    }
                    
                    // 检查如果下一步需要动态指定审批人，是否已选择
                    var nextApproverDiv = $("#nextApproverDiv");
                    console.log("下一步审批人选择框是否可见:", nextApproverDiv.is(":visible"));
                    console.log("后台变量是否需要动态指定下一步审批人:", nextStepNeedsDynamicApprover);
                    
                    if (nextApproverDiv.is(":visible")) {
                        var nextApprover = $("#nextApprover").val();
                        console.log("选择的下一步审批人:", nextApprover);
                        if (!nextApprover) {
                            alert("请选择下一步审批人");
                            return;
                        }
                    }
                    
                    // 检查是否有文件正在上传
                    if (!checkSubmitEnabled()) {
                        console.log("文件正在上传，不能提交表单");
                        alert("请等待文件上传完成后再提交！");
                        return;
                    }
                    
                    $("#approve-form").submit();
                });                  $("#reject-btn").on("click", function() {
                    console.log("拒绝按钮被点击");
                    
                    // 校验表单
                    var form = $("#reject-form")[0];
                    if (!form.checkValidity()) {
                        console.log("拒绝表单验证失败");
                        // 添加was-validated类以显示验证消息
                        $(form).addClass("was-validated");
                        return;
                    }
                    
                    $("#reject-form").submit();
                });
                
                // 调试: 直接打印人员数据对象
                console.log("人员数据(JavaScript变量):", personnelData);
                console.log("人员数据类型:", typeof personnelData);
                console.log("人员数据是否为数组:", Array.isArray(personnelData));
                console.log("人员数据长度:", personnelData ? personnelData.length : 0);
                
                // 调试: 检查HTML中的选项数量
                var nextApproverElement = document.getElementById('nextApprover');
                var toUsernameElement = document.getElementById('toUsername');
                
                if(nextApproverElement) {
                    console.log("审批人下拉框中的选项数量:", nextApproverElement.options.length);
                    console.log("审批人下拉框中的选项:");
                    for(var i = 0; i < Math.min(nextApproverElement.options.length, 5); i++) {
                        console.log(" - 选项", i, ":", nextApproverElement.options[i].text);
                    }
                }
                
                if(toUsernameElement) {
                    console.log("转交人员下拉框中的选项数量:", toUsernameElement.options.length);
                    console.log("转交人员下拉框中的选项:");
                    for(var i = 0; i < Math.min(toUsernameElement.options.length, 5); i++) {
                        console.log(" - 选项", i, ":", toUsernameElement.options[i].text);
                    }
                }
                  // 通用的安全Select2初始化函数
                function safeInitSelect2(selector, placeholder) {
                    try {
                        var $select = $(selector);
                        if ($select.length === 0) {
                            console.warn("Select2初始化：未找到元素", selector);
                            return false;
                        }
                        
                        // 清理无效选项
                        $select.find('option').each(function() {
                            var $option = $(this);
                            var value = $option.val();
                            var text = $option.text();
                            
                            // 跳过第一个选项（通常是占位符）
                            if ($option.index() === 0) return;
                            
                            // 如果值为null、undefined或空字符串，移除选项
                            if (value === null || value === undefined || 
                                (typeof value === 'string' && value.trim() === '')) {
                                console.warn('移除无效选项:', selector, value, text);
                                $option.remove();
                            }
                            // 如果文本为空，使用值作为文本
                            else if (!text || text.trim() === '') {
                                console.warn('修复无文本选项:', selector, value);
                                $option.text(String(value));
                            }
                        });
                        
                        // 初始化Select2
                        $select.select2({
                            theme: "bootstrap-5",
                            width: '100%',
                            placeholder: placeholder || "请选择",
                            allowClear: true,
                            escapeMarkup: function(markup) {
                                if (markup === null || markup === undefined) {
                                    return '';
                                }
                                return String(markup);
                            },
                            templateResult: function(data) {
                                if (!data || data.text === null || data.text === undefined) {
                                    return '';
                                }
                                var safeText = String(data.text).trim();
                                return safeText ? $('<span>').text(safeText) : '';
                            },
                            templateSelection: function(data) {
                                if (!data || data.text === null || data.text === undefined) {
                                    return '';
                                }
                                return String(data.text).trim();
                            }
                        });
                        
                        console.log("Select2初始化成功:", selector);
                        return true;
                    } catch(e) {
                        console.error("Select2初始化失败:", selector, e);
                        return false;
                    }
                }
                  // 使用安全初始化函数，添加延迟以确保DOM完全加载
                setTimeout(function() {
                    safeInitSelect2("#toUsername", "请选择转交人员");
                    safeInitSelect2("#nextApprover", "请选择审批人");
                }, 100);
                
                // 对页面上所有的select元素进行安全检查和可能的初始化
                $("select").each(function() {
                    var $select = $(this);
                    var id = $select.attr("id") || "未知ID";
                    
                    // 如果还没有被Select2初始化，进行基本的安全检查
                    if (!$select.hasClass('select2-hidden-accessible')) {
                        $select.find('option').each(function() {
                            var $option = $(this);
                            var value = $option.val();
                            var text = $option.text();
                            
                            if (value === null || value === undefined) {
                                console.warn('发现null/undefined选项值:', id, $option.index());
                                $option.val('');
                            }
                            if (text === null || text === undefined) {
                                console.warn('发现null/undefined选项文本:', id, $option.index());
                                $option.text('');
                            }
                        });
                    }
                });
                  // 打印所有select元素
                $("select").each(function() {
                    var id = $(this).attr("id") || "未知ID";
                    var options = $(this).find("option").length;
                    console.log("发现select元素:", id, "选项数:", options);
                    
                    // 检查每个选项的值和文本是否安全
                    $(this).find("option").each(function(index) {
                        var value = $(this).val() || '';
                        var text = $(this).text() || '';
                        if (index < 3) { // 只打印前3个选项避免日志过多
                            console.log("  选项" + index + ":", "value='" + value + "', text='" + text + "'");
                        }                    });
                });
                  // 页面卸载时清理临时文件
                window.addEventListener('beforeunload', function(event) {
                    // 收集所有需要删除的临时文件路径
                    const tempFilePaths = [];
                    
                    const tempFilePath1 = $("#tempFilePath1").val();
                    const tempFilePath2 = $("#tempFilePath2").val();
                    
                    if (tempFilePath1 && tempFilePath1.trim() !== '') {
                        tempFilePaths.push(tempFilePath1.trim());
                    }
                    if (tempFilePath2 && tempFilePath2.trim() !== '') {
                        tempFilePaths.push(tempFilePath2.trim());
                    }
                    
                    // 异步删除临时文件（不等待完成）
                    if (tempFilePaths.length > 0) {
                        console.log("页面卸载时清理临时文件:", tempFilePaths);
                        
                        const token = $("meta[name='_csrf']").attr("content");
                        const header = $("meta[name='_csrf_header']").attr("content");
                          tempFilePaths.forEach(function(filePath) {
                            // 使用sendBeacon API进行可靠的异步请求（POST方法）
                            if (navigator.sendBeacon) {
                                const formData = new FormData();
                                formData.append('filePath', filePath);
                                if (header && token) {
                                    formData.append(header.replace('X-', ''), token);
                                }
                                // sendBeacon使用POST方法，发送到POST端点
                                navigator.sendBeacon('/api/files/delete-temp', formData);                            } else {
                                // 降级到同步请求（DELETE方法）
                                $.ajax({
                                    url: '/api/files/delete-temp',
                                    type: 'DELETE',
                                    async: false,
                                    data: { filePath: filePath },
                                    dataType: 'json',
                                    headers: {
                                        'Accept': 'application/json',
                                        'X-Requested-With': 'XMLHttpRequest'
                                    },
                                    beforeSend: function(xhr) {
                                        if (header && token) {
                                            xhr.setRequestHeader(header, token);
                                        }
                                    }
                                });
                            }
                        });
                    }
                });
                
                console.log("=== 审批表单初始化完成 ===");
            });
        </script>
    </div>
</body>
</html>
